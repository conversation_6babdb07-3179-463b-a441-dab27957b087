#!/usr/bin/env python3

import frappe
from frappe.utils import flt

def test_item_rate_total_value():
    """Test the total value calculation for Item Rate projects"""
    print("Testing Item Rate total value calculation...")
    
    # Check if there are any existing projects
    projects = frappe.get_all("Projects", 
                             filters={"contract_type": "Item Rate"}, 
                             fields=["name", "project_name", "total_value"])
    
    print(f"Found {len(projects)} Item Rate projects")
    
    if projects:
        for project in projects:
            print(f"\nProject: {project.project_name}")
            print(f"Current Total Value: {project.total_value}")
            
            # Get the project document and recalculate
            project_doc = frappe.get_doc("Projects", project.name)
            project_doc.calculate_total_value()
            print(f"Calculated Total Value: {project_doc.total_value}")
            
            # Check BOQs for this project
            boqs = frappe.get_all("BOQ", 
                                 filters={"project": project.name}, 
                                 fields=["name", "grand_total"])
            
            print(f"BOQs for this project: {len(boqs)}")
            total_from_boqs = 0
            for boq in boqs:
                grand_total = flt(boq.grand_total or 0)
                print(f"  BOQ {boq.name}: Grand Total = {grand_total}")
                total_from_boqs += grand_total
            
            print(f"Manual sum of BOQ grand totals: {total_from_boqs}")
            
            # Check if calculation matches
            if project_doc.total_value == total_from_boqs:
                print("✓ Total value calculation is CORRECT!")
            else:
                print("✗ Total value calculation is INCORRECT!")
                print(f"Expected: {total_from_boqs}, Got: {project_doc.total_value}")
            
            print("-" * 50)
    else:
        print("No Item Rate projects found.")
        print("Please create a project with contract type 'Item Rate' and some BOQs to test.")

def test_built_up_total_value():
    """Test the total value calculation for Built Up projects"""
    print("\nTesting Built Up total value calculation...")
    
    # Check if there are any existing Built Up projects
    projects = frappe.get_all("Projects", 
                             filters={"contract_type": "Built Up"}, 
                             fields=["name", "project_name", "total_value", "area", "rate"])
    
    print(f"Found {len(projects)} Built Up projects")
    
    if projects:
        for project in projects:
            print(f"\nProject: {project.project_name}")
            print(f"Current Total Value: {project.total_value}")
            print(f"Area: {project.area}, Rate: {project.rate}")
            
            # Get the project document and recalculate
            project_doc = frappe.get_doc("Projects", project.name)
            project_doc.calculate_total_value()
            print(f"Calculated Total Value: {project_doc.total_value}")
            
            # Manual calculation
            expected_total = flt(project.area or 0) * flt(project.rate or 0)
            print(f"Expected Total (area * rate): {expected_total}")
            
            # Check if calculation matches
            if project_doc.total_value == expected_total:
                print("✓ Total value calculation is CORRECT!")
            else:
                print("✗ Total value calculation is INCORRECT!")
                print(f"Expected: {expected_total}, Got: {project_doc.total_value}")
            
            print("-" * 50)
    else:
        print("No Built Up projects found.")

if __name__ == "__main__":
    frappe.init(site="all")
    frappe.connect()
    
    test_item_rate_total_value()
    test_built_up_total_value()
